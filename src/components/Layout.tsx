import { AnimatePresence, motion } from 'framer-motion'
import { AlertTriangle, ArrowLeft, Edit3, Home } from 'lucide-react'
import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

interface LayoutProps {
  children: React.ReactNode
}

/**
 * 布局组件
 * 提供统一的页面布局和导航功能
 * 支持响应式设计：手机、平板、桌面
 */
const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [showDisclaimer, setShowDisclaimer] = useState(false)

  // 判断是否为首页
  const isHomePage = location.pathname === '/'
  // 判断是否为健康咨询页面
  const isConsultationPage = location.pathname === '/consultation'

  // 获取页面标题
  const getPageTitle = () => {
    const titles: Record<string, string> = {
      '/': '个人健康助理',
      '/report': '报告解读',
      '/consultation': 'AI健康咨询',
      '/assessment': '症状自评',
      '/trend': '趋势分析',
    }
    return titles[location.pathname] || '健康助理'
  }

  // 返回首页
  const goHome = () => {
    console.log('🏠 返回首页')
    navigate('/')
  }

  // 返回上一页
  const goBack = () => {
    console.log('⬅️ 返回上一页')
    window.history.back()
  }

  // 清空对话（仅健康咨询页面）
  const clearConversation = () => {
    console.log('🔄 清空对话')
    // 发送自定义事件给健康咨询页面
    window.dispatchEvent(new CustomEvent('clearConversation'))
  }

  // 🔥 健康咨询页面的特殊标题栏布局
  const renderConsultationHeader = () => (
    <div className="flex items-center justify-between w-full">
      {/* 左侧：Home图标 */}
      <div className="flex items-center">
        <button
          onClick={goHome}
          className="p-2 -ml-2 rounded-full hover:bg-gray-100 transition-colors"
          title="返回首页"
        >
          <Home className="w-5 h-5 text-gray-600" />
        </button>
      </div>

      {/* 中间：标题居中 + 警告图标 */}
      <div className="flex items-center space-x-2">
        <h1 className="text-lg font-semibold text-gray-900">AI健康咨询</h1>
        <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
        <button
          onClick={() => {
            console.log('🔄 显示免责声明', showDisclaimer, isConsultationPage)
            setShowDisclaimer(!showDisclaimer)
          }}
          className="text-amber-600 hover:text-amber-700 transition-colors p-1"
          title="重要提示"
        >
          <AlertTriangle className="w-4 h-4" />
        </button>
      </div>

      {/* 右侧：编辑图标（新对话） */}
      <div className="flex items-center">
        <button
          onClick={clearConversation}
          className="p-2 -mr-2 rounded-full hover:bg-gray-100 transition-colors"
          title="新对话"
        >
          <Edit3 className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    </div>
  )

  // 🔥 普通页面的标题栏布局
  const renderNormalHeader = () => (
    <>
      {/* 左侧按钮 */}
      <div className="w-10">
        {!isHomePage && (
          <button
            onClick={goHome}
            className="p-2 -mr-2 rounded-full hover:bg-gray-100 transition-colors"
            aria-label="首页"
          >
            <Home className="w-5 h-5 text-gray-600" />
          </button>
        )}
      </div>

      {/* 中间标题 */}
      <h1 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">{getPageTitle()}</h1>

      {/* 右侧按钮--假的，隐藏 */}
      <div className="w-10">
        {
          <button
            onClick={goBack}
            className="p-2 -ml-2 rounded-full hover:bg-gray-100 transition-colors hidden"
            aria-label="返回"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
        }
      </div>
    </>
  )

  return (
    <div className="min-h-screen bg-gray-50 safe-area-top safe-area-bottom">
      {/* 顶部导航栏 - 响应式设计 */}
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100"
      >
        <div className="flex items-center justify-between px-4 sm:px-6 lg:px-8 py-3 w-full">
          {/* 🔥 根据页面类型渲染不同的标题栏 */}
          {isConsultationPage ? renderConsultationHeader() : renderNormalHeader()}
        </div>
      </motion.header>

      {/* 🔥 健康咨询页面的免责声明 - 修复AnimatePresence，提高z-index */}
      <AnimatePresence>
        {isConsultationPage && showDisclaimer && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="sticky top-0 z-[60] bg-amber-50 border-b border-amber-200 px-4 py-3 shadow-sm"
            style={{
              position: 'fixed',
              zIndex: 60,
              backgroundColor: '#fef3c7',
              width: 'calc(100% - 20px)',
              margin: '0 auto',
              left: '10px',
              right: '10px',
              top: '65px',
              borderRadius: '15px',
            }}
          >
            <div className="flex items-start space-x-2 text-amber-700 max-w-full">
              <span className="text-sm mt-0.5">⚠️</span>
              <div className="flex-1">
                <p className="text-xs leading-relaxed">
                  <span className="font-medium">重要提示：</span>
                  我的建议仅供参考，不能替代医生的专业诊断。如有急重症状或持续不适，请及时前往医院就诊。
                </p>
              </div>
              <button
                onClick={() => {
                  console.log('❌ 关闭免责声明')
                  setShowDisclaimer(false)
                }}
                className="text-amber-600 hover:text-amber-800 transition-colors"
              >
                ×
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 主要内容区域 - 🔥 健康咨询页面全宽显示，不添加底部安全区域 */}
      <main
        className={`flex-1 ${isConsultationPage ? 'pb-0' : 'pb-6 safe-area-bottom-enhanced mobile-safe-bottom'}`}
      >
        <div
          className={`w-full mx-auto ${isConsultationPage ? '' : 'md:max-w-2xl lg:max-w-4xl xl:max-w-5xl px-4 sm:px-6 lg:px-8'}`}
        >
          {children}
        </div>
      </main>

      {/* 底部信息（仅在首页显示） */}
      {isHomePage && (
        <motion.footer
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center py-6 text-sm text-gray-500"
        >
          <p>个人健康助理 v1.0.0</p>
          <p className="mt-1">智能解读 · 专业咨询 · 健康管理</p>
        </motion.footer>
      )}

      {/* 全局提示容器 */}
      <div id="toast-container" className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50" />
    </div>
  )
}

export default Layout
